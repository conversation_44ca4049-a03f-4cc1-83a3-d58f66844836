import { v4 as uuidv4 } from 'uuid';
import {
  Diff<PERSON><PERSON>ult,
  VisualDiff,
  ApplicationState,
  ChangeSet,
  ChangeType,
  DiffStatistics,
  VisualDiffType,
  TextDiff,
  TextDiffLine,
  TextDiffLineType,
  TextDiffStatistics,
  StructuralDiff,
  StructuralDiffElement,
  StructuralDiffStatistics,
  ImageData,
} from '../../shared/types/Timeline';

export interface DiffEngineConfig {
  textDiffAlgorithm?: 'myers' | 'patience' | 'histogram';
  contextLines?: number;
  ignoreWhitespace?: boolean;
  ignoreCase?: boolean;
  maxDiffSize?: number;
  enableVisualDiff?: boolean;
  visualDiffThreshold?: number;
}

export class DiffEngine {
  private config: Required<DiffEngineConfig>;

  constructor(config: DiffEngineConfig = {}) {
    this.config = {
      textDiffAlgorithm: config.textDiffAlgorithm || 'myers',
      contextLines: config.contextLines || 3,
      ignoreWhitespace: config.ignoreWhitespace || false,
      ignoreCase: config.ignoreCase || false,
      maxDiffSize: config.maxDiffSize || 10000000, // 10MB
      enableVisualDiff: config.enableVisualDiff ?? true,
      visualDiffThreshold: config.visualDiffThreshold || 0.1,
    };
  }

  async createDiff(before: ApplicationState, after: ApplicationState): Promise<DiffResult> {
    const beforeCheckpoint = before.id;
    const afterCheckpoint = after.id;

    try {
      const changes: ChangeSet[] = [];

      // Compare documents
      const documentChanges = await this.compareDocuments(before.documents, after.documents);
      changes.push(...documentChanges);

      // Compare knowledge base
      const knowledgeChanges = await this.compareKnowledgeBase(
        before.knowledgeBase,
        after.knowledgeBase
      );
      changes.push(...knowledgeChanges);

      // Compare UI state
      const uiChanges = await this.compareUIState(before.userInterface, after.userInterface);
      changes.push(...uiChanges);

      // Compare AI context
      const aiChanges = await this.compareAIContext(before.aiContext, after.aiContext);
      changes.push(...aiChanges);

      // Compare configuration
      const configChanges = await this.compareConfiguration(
        before.configuration,
        after.configuration
      );
      changes.push(...configChanges);

      const statistics = this.calculateDiffStatistics(changes);

      const diffResult: DiffResult = {
        id: uuidv4(),
        beforeCheckpoint,
        afterCheckpoint,
        changes,
        statistics,
        createdAt: new Date(),
      };

      return diffResult;
    } catch (error) {
      throw new Error(
        `Failed to create diff: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  async createVisualDiff(before: any, after: any): Promise<VisualDiff> {
    const startTime = Date.now();

    try {
      // Determine the type of visual diff based on input
      let diffType: VisualDiffType;
      let textDiff: TextDiff | undefined;
      let structuralDiff: StructuralDiff | undefined;

      if (typeof before === 'string' && typeof after === 'string') {
        diffType = VisualDiffType.TEXT;
        textDiff = await this.createTextDiff(before, after);
      } else if (this.isImageData(before) && this.isImageData(after)) {
        diffType = VisualDiffType.IMAGE;
        // Image diff would be implemented here
      } else if (this.isPDFData(before) && this.isPDFData(after)) {
        diffType = VisualDiffType.PDF;
        // PDF diff would be implemented here
      } else {
        diffType = VisualDiffType.STRUCTURAL;
        structuralDiff = await this.createStructuralDiff(before, after);
      }

      const processingTime = Date.now() - startTime;

      const visualDiff: VisualDiff = {
        id: uuidv4(),
        type: diffType,
        ...(textDiff && { textDiff }),
        ...(structuralDiff && { structuralDiff }),
        metadata: {
          algorithm: this.config.textDiffAlgorithm,
          threshold: this.config.visualDiffThreshold,
          processingTime,
          accuracy: 0.95, // This would be calculated based on the diff algorithm
          createdAt: new Date(),
        },
      };

      return visualDiff;
    } catch (error) {
      throw new Error(
        `Failed to create visual diff: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  private async createTextDiff(before: string, after: string): Promise<TextDiff> {
    const beforeLines = before.split('\n');
    const afterLines = after.split('\n');

    const diffLines: TextDiffLine[] = [];
    let beforeLineNumber = 1;
    let afterLineNumber = 1;

    // Simple line-by-line diff implementation
    // In a real implementation, you'd use a more sophisticated algorithm like Myers
    const maxLines = Math.max(beforeLines.length, afterLines.length);

    for (let i = 0; i < maxLines; i++) {
      const beforeLine = beforeLines[i];
      const afterLine = afterLines[i];

      if (beforeLine === undefined) {
        // Line added
        diffLines.push({
          lineNumber: afterLineNumber,
          type: TextDiffLineType.ADDED,
          content: afterLine || '',
          afterLineNumber: afterLineNumber,
        });
        afterLineNumber++;
      } else if (afterLine === undefined) {
        // Line deleted
        diffLines.push({
          lineNumber: beforeLineNumber,
          type: TextDiffLineType.DELETED,
          content: beforeLine,
          beforeLineNumber: beforeLineNumber,
        });
        beforeLineNumber++;
      } else if (beforeLine === afterLine) {
        // Line unchanged
        diffLines.push({
          lineNumber: beforeLineNumber,
          type: TextDiffLineType.UNCHANGED,
          content: beforeLine,
          beforeLineNumber: beforeLineNumber,
          afterLineNumber: afterLineNumber,
        });
        beforeLineNumber++;
        afterLineNumber++;
      } else {
        // Line modified
        diffLines.push({
          lineNumber: beforeLineNumber,
          type: TextDiffLineType.MODIFIED,
          content: afterLine,
          beforeLineNumber: beforeLineNumber,
          afterLineNumber: afterLineNumber,
        });
        beforeLineNumber++;
        afterLineNumber++;
      }
    }

    const statistics: TextDiffStatistics = {
      totalLines: diffLines.length,
      addedLines: diffLines.filter(line => line.type === TextDiffLineType.ADDED).length,
      deletedLines: diffLines.filter(line => line.type === TextDiffLineType.DELETED).length,
      modifiedLines: diffLines.filter(line => line.type === TextDiffLineType.MODIFIED).length,
      unchangedLines: diffLines.filter(line => line.type === TextDiffLineType.UNCHANGED).length,
    };

    return {
      lines: diffLines,
      statistics,
    };
  }

  private async createStructuralDiff(before: any, after: any): Promise<StructuralDiff> {
    const elements: StructuralDiffElement[] = [];

    // Deep comparison of object structures
    const beforeKeys = Object.keys(before || {});
    const afterKeys = Object.keys(after || {});
    const allKeys = new Set([...beforeKeys, ...afterKeys]);

    for (const key of allKeys) {
      const beforeValue = before?.[key];
      const afterValue = after?.[key];

      if (beforeValue === undefined && afterValue !== undefined) {
        elements.push({
          id: uuidv4(),
          type: typeof afterValue,
          changeType: ChangeType.ADDED,
          path: key,
          afterProperties: { [key]: afterValue },
        });
      } else if (beforeValue !== undefined && afterValue === undefined) {
        elements.push({
          id: uuidv4(),
          type: typeof beforeValue,
          changeType: ChangeType.DELETED,
          path: key,
          beforeProperties: { [key]: beforeValue },
        });
      } else if (beforeValue !== afterValue) {
        elements.push({
          id: uuidv4(),
          type: typeof afterValue,
          changeType: ChangeType.MODIFIED,
          path: key,
          beforeProperties: { [key]: beforeValue },
          afterProperties: { [key]: afterValue },
        });
      }
    }

    const statistics: StructuralDiffStatistics = {
      totalElements: elements.length,
      addedElements: elements.filter(el => el.changeType === ChangeType.ADDED).length,
      modifiedElements: elements.filter(el => el.changeType === ChangeType.MODIFIED).length,
      deletedElements: elements.filter(el => el.changeType === ChangeType.DELETED).length,
      movedElements: elements.filter(el => el.changeType === ChangeType.MOVED).length,
    };

    return {
      elements,
      statistics,
    };
  }

  private async compareDocuments(before: any[], after: any[]): Promise<ChangeSet[]> {
    const changes: ChangeSet[] = [];

    // Create maps for efficient lookup
    const beforeMap = new Map(before.map(doc => [doc.documentId, doc]));
    const afterMap = new Map(after.map(doc => [doc.documentId, doc]));

    // Find added documents
    for (const [id, doc] of afterMap) {
      if (!beforeMap.has(id)) {
        changes.push({
          type: ChangeType.ADDED,
          path: `documents.${id}`,
          beforeValue: undefined,
          afterValue: doc,
          confidence: 1.0,
          description: `Document ${id} was added`,
        });
      }
    }

    // Find deleted documents
    for (const [id, doc] of beforeMap) {
      if (!afterMap.has(id)) {
        changes.push({
          type: ChangeType.DELETED,
          path: `documents.${id}`,
          beforeValue: doc,
          afterValue: undefined,
          confidence: 1.0,
          description: `Document ${id} was deleted`,
        });
      }
    }

    // Find modified documents
    for (const [id, beforeDoc] of beforeMap) {
      const afterDoc = afterMap.get(id);
      if (afterDoc && JSON.stringify(beforeDoc) !== JSON.stringify(afterDoc)) {
        changes.push({
          type: ChangeType.MODIFIED,
          path: `documents.${id}`,
          beforeValue: beforeDoc,
          afterValue: afterDoc,
          confidence: 1.0,
          description: `Document ${id} was modified`,
        });
      }
    }

    return changes;
  }

  private async compareKnowledgeBase(before: any, after: any): Promise<ChangeSet[]> {
    const changes: ChangeSet[] = [];

    if (JSON.stringify(before) !== JSON.stringify(after)) {
      changes.push({
        type: ChangeType.MODIFIED,
        path: 'knowledgeBase',
        beforeValue: before,
        afterValue: after,
        confidence: 0.9,
        description: 'Knowledge base was modified',
      });
    }

    return changes;
  }

  private async compareUIState(before: any, after: any): Promise<ChangeSet[]> {
    const changes: ChangeSet[] = [];

    if (JSON.stringify(before) !== JSON.stringify(after)) {
      changes.push({
        type: ChangeType.MODIFIED,
        path: 'userInterface',
        beforeValue: before,
        afterValue: after,
        confidence: 0.8,
        description: 'UI state was modified',
      });
    }

    return changes;
  }

  private async compareAIContext(before: any, after: any): Promise<ChangeSet[]> {
    const changes: ChangeSet[] = [];

    if (JSON.stringify(before) !== JSON.stringify(after)) {
      changes.push({
        type: ChangeType.MODIFIED,
        path: 'aiContext',
        beforeValue: before,
        afterValue: after,
        confidence: 0.9,
        description: 'AI context was modified',
      });
    }

    return changes;
  }

  private async compareConfiguration(before: any, after: any): Promise<ChangeSet[]> {
    const changes: ChangeSet[] = [];

    if (JSON.stringify(before) !== JSON.stringify(after)) {
      changes.push({
        type: ChangeType.MODIFIED,
        path: 'configuration',
        beforeValue: before,
        afterValue: after,
        confidence: 1.0,
        description: 'Configuration was modified',
      });
    }

    return changes;
  }

  private calculateDiffStatistics(changes: ChangeSet[]): DiffStatistics {
    return {
      totalChanges: changes.length,
      addedItems: changes.filter(c => c.type === ChangeType.ADDED).length,
      modifiedItems: changes.filter(c => c.type === ChangeType.MODIFIED).length,
      deletedItems: changes.filter(c => c.type === ChangeType.DELETED).length,
      movedItems: changes.filter(c => c.type === ChangeType.MOVED).length,
      renamedItems: changes.filter(c => c.type === ChangeType.RENAMED).length,
    };
  }

  private isImageData(data: any): data is ImageData {
    return (
      data && typeof data === 'object' && 'width' in data && 'height' in data && 'data' in data
    );
  }

  private isPDFData(data: any): boolean {
    return data && typeof data === 'object' && data.type === 'pdf';
  }
}
